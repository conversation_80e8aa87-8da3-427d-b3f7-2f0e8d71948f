/**
 * React Hook for WebSocket workflow updates
 * Provides real-time updates for Manual Build workflows
 */

import { useEffect, useRef, useState, useCallback } from 'react';
import { WorkflowWebSocketEvent } from '@/lib/websocket/WorkflowWebSocketServer';

export interface WorkflowWebSocketState {
  isConnected: boolean;
  isConnecting: boolean;
  error: string | null;
  lastEvent: WorkflowWebSocketEvent | null;
  events: WorkflowWebSocketEvent[];
  connectionCount: number;
}

export interface WorkflowWebSocketActions {
  connect: () => void;
  disconnect: () => void;
  sendEvent: (eventType: string, data: any, executionId?: string) => Promise<void>;
  clearEvents: () => void;
  reconnect: () => void;
}

export function useWorkflowWebSocket(
  workflowId: string | null,
  options: {
    autoConnect?: boolean;
    maxEvents?: number;
    reconnectInterval?: number;
    onEvent?: (event: WorkflowWebSocketEvent) => void;
    onConnect?: () => void;
    onDisconnect?: () => void;
    onError?: (error: string) => void;
  } = {}
): [WorkflowWebSocketState, WorkflowWebSocketActions] {
  const {
    autoConnect = true,
    maxEvents = 100,
    reconnectInterval = 5000,
    onEvent,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [state, setState] = useState<WorkflowWebSocketState>({
    isConnected: false,
    isConnecting: false,
    error: null,
    lastEvent: null,
    events: [],
    connectionCount: 0
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isManualDisconnectRef = useRef(false);

  // Connect to WebSocket
  const connect = useCallback(() => {
    if (!workflowId || state.isConnected || state.isConnecting) {
      return;
    }

    setState(prev => ({ ...prev, isConnecting: true, error: null }));
    isManualDisconnectRef.current = false;

    try {
      const eventSource = new EventSource(`/api/workflow/stream/${workflowId}`);
      eventSourceRef.current = eventSource;

      eventSource.onopen = () => {
        console.log(`[Workflow WebSocket] Connected to workflow ${workflowId}`);
        setState(prev => ({
          ...prev,
          isConnected: true,
          isConnecting: false,
          error: null,
          connectionCount: prev.connectionCount + 1
        }));
        onConnect?.();
      };

      eventSource.onmessage = (event) => {
        try {
          const workflowEvent: WorkflowWebSocketEvent = JSON.parse(event.data);
          
          setState(prev => {
            const newEvents = [...prev.events, workflowEvent];
            // Keep only the last maxEvents
            if (newEvents.length > maxEvents) {
              newEvents.splice(0, newEvents.length - maxEvents);
            }

            return {
              ...prev,
              lastEvent: workflowEvent,
              events: newEvents
            };
          });

          onEvent?.(workflowEvent);
          console.log(`[Workflow WebSocket] Received event:`, workflowEvent);
        } catch (error) {
          console.error('[Workflow WebSocket] Failed to parse event:', error);
        }
      };

      eventSource.onerror = (error) => {
        console.error(`[Workflow WebSocket] Connection error:`, error);
        
        setState(prev => ({
          ...prev,
          isConnected: false,
          isConnecting: false,
          error: 'Connection error'
        }));

        onError?.('Connection error');

        // Auto-reconnect if not manually disconnected
        if (!isManualDisconnectRef.current && reconnectInterval > 0) {
          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`[Workflow WebSocket] Attempting to reconnect...`);
            connect();
          }, reconnectInterval);
        }
      };

    } catch (error) {
      console.error('[Workflow WebSocket] Failed to create connection:', error);
      setState(prev => ({
        ...prev,
        isConnecting: false,
        error: 'Failed to create connection'
      }));
      onError?.('Failed to create connection');
    }
  }, [workflowId, state.isConnected, state.isConnecting, maxEvents, reconnectInterval, onConnect, onEvent, onError]);

  // Disconnect from WebSocket
  const disconnect = useCallback(() => {
    isManualDisconnectRef.current = true;
    
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }

    setState(prev => ({
      ...prev,
      isConnected: false,
      isConnecting: false,
      error: null
    }));

    onDisconnect?.();
    console.log(`[Workflow WebSocket] Disconnected from workflow ${workflowId}`);
  }, [workflowId, onDisconnect]);

  // Send event to workflow
  const sendEvent = useCallback(async (
    eventType: string,
    data: any,
    executionId?: string
  ): Promise<void> => {
    if (!workflowId) {
      throw new Error('No workflow ID provided');
    }

    try {
      const response = await fetch(`/api/workflow/stream/${workflowId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          eventType,
          data,
          executionId
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send event: ${response.statusText}`);
      }

      console.log(`[Workflow WebSocket] Sent event ${eventType} to workflow ${workflowId}`);
    } catch (error) {
      console.error('[Workflow WebSocket] Failed to send event:', error);
      throw error;
    }
  }, [workflowId]);

  // Clear events history
  const clearEvents = useCallback(() => {
    setState(prev => ({
      ...prev,
      events: [],
      lastEvent: null
    }));
  }, []);

  // Reconnect
  const reconnect = useCallback(() => {
    disconnect();
    setTimeout(connect, 100);
  }, [disconnect, connect]);

  // Auto-connect on mount or workflowId change
  useEffect(() => {
    if (autoConnect && workflowId && !state.isConnected && !state.isConnecting) {
      connect();
    }

    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [workflowId, autoConnect, connect, state.isConnected, state.isConnecting]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnect();
    };
  }, [disconnect]);

  const actions: WorkflowWebSocketActions = {
    connect,
    disconnect,
    sendEvent,
    clearEvents,
    reconnect
  };

  return [state, actions];
}
